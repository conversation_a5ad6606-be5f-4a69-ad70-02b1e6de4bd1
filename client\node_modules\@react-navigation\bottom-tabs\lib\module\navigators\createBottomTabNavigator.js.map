{"version": 3, "names": ["createNavigatorFactory", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useNavigationBuilder", "BottomTabView", "jsx", "_jsx", "BottomTabNavigator", "id", "initialRouteName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "layout", "screenListeners", "screenOptions", "screenLayout", "UNSTABLE_router", "rest", "state", "descriptors", "navigation", "NavigationContent", "createBottomTabNavigator", "config"], "sourceRoot": "../../../src", "sources": ["navigators/createBottomTabNavigator.tsx"], "mappings": ";;AAAA,SACEA,sBAAsB,EAMtBC,SAAS,EAGTC,oBAAoB,QACf,0BAA0B;AAQjC,SAASC,aAAa,QAAQ,2BAAwB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAEvD,SAASC,kBAAkBA,CAAC;EAC1BC,EAAE;EACFC,gBAAgB;EAChBC,YAAY;EACZC,QAAQ;EACRC,MAAM;EACNC,eAAe;EACfC,aAAa;EACbC,YAAY;EACZC,eAAe;EACf,GAAGC;AACoB,CAAC,EAAE;EAC1B,MAAM;IAAEC,KAAK;IAAEC,WAAW;IAAEC,UAAU;IAAEC;EAAkB,CAAC,GACzDlB,oBAAoB,CAMlBD,SAAS,EAAE;IACXM,EAAE;IACFC,gBAAgB;IAChBC,YAAY;IACZC,QAAQ;IACRC,MAAM;IACNC,eAAe;IACfC,aAAa;IACbC,YAAY;IACZC;EACF,CAAC,CAAC;EAEJ,oBACEV,IAAA,CAACe,iBAAiB;IAAAV,QAAA,eAChBL,IAAA,CAACF,aAAa;MAAA,GACRa,IAAI;MACRC,KAAK,EAAEA,KAAM;MACbE,UAAU,EAAEA,UAAW;MACvBD,WAAW,EAAEA;IAAY,CAC1B;EAAC,CACe,CAAC;AAExB;AAEA,OAAO,SAASG,wBAAwBA,CAmBtCC,MAAe,EAAmC;EAClD,OAAOtB,sBAAsB,CAACM,kBAAkB,CAAC,CAACgB,MAAM,CAAC;AAC3D", "ignoreList": []}