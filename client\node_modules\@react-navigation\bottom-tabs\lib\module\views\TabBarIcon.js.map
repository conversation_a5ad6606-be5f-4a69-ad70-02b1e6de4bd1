{"version": 3, "names": ["React", "StyleSheet", "View", "Badge", "jsx", "_jsx", "jsxs", "_jsxs", "ICON_SIZE_WIDE", "ICON_SIZE_WIDE_COMPACT", "ICON_SIZE_TALL", "ICON_SIZE_TALL_COMPACT", "ICON_SIZE_ROUND", "ICON_SIZE_ROUND_COMPACT", "ICON_SIZE_MATERIAL", "TabBarIcon", "route", "_", "variant", "size", "badge", "badgeStyle", "activeOpacity", "inactiveOpacity", "activeTintColor", "inactiveTintColor", "renderIcon", "allowFontScaling", "style", "iconSize", "styles", "wrapperMaterial", "wrapperUikitCompact", "wrapperUikit", "children", "icon", "opacity", "min<PERSON><PERSON><PERSON>", "focused", "color", "visible", "create", "position", "alignSelf", "alignItems", "justifyContent", "height", "width", "end", "top"], "sourceRoot": "../../../src", "sources": ["views/TabBarIcon.tsx"], "mappings": ";;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAEEC,UAAU,EAEVC,IAAI,QAEC,cAAc;AAErB,SAASC,KAAK,QAAQ,YAAS;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAqBhC;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG,EAAE;AACzB,MAAMC,sBAAsB,GAAG,EAAE;AACjC,MAAMC,cAAc,GAAG,EAAE;AACzB,MAAMC,sBAAsB,GAAG,EAAE;AACjC,MAAMC,eAAe,GAAG,EAAE;AAC1B,MAAMC,uBAAuB,GAAG,EAAE;AAClC,MAAMC,kBAAkB,GAAG,EAAE;AAE7B,OAAO,SAASC,UAAUA,CAAC;EACzBC,KAAK,EAAEC,CAAC;EACRC,OAAO;EACPC,IAAI;EACJC,KAAK;EACLC,UAAU;EACVC,aAAa;EACbC,eAAe;EACfC,eAAe;EACfC,iBAAiB;EACjBC,UAAU;EACVC,gBAAgB;EAChBC;AACK,CAAC,EAAE;EACR,MAAMC,QAAQ,GACZX,OAAO,KAAK,UAAU,GAClBJ,kBAAkB,GAClBK,IAAI,KAAK,SAAS,GAChBN,uBAAuB,GACvBD,eAAe;;EAEvB;EACA;EACA,oBACEL,KAAA,CAACL,IAAI;IACH0B,KAAK,EAAE,CACLV,OAAO,KAAK,UAAU,GAClBY,MAAM,CAACC,eAAe,GACtBZ,IAAI,KAAK,SAAS,GAChBW,MAAM,CAACE,mBAAmB,GAC1BF,MAAM,CAACG,YAAY,EACzBL,KAAK,CACL;IAAAM,QAAA,gBAEF7B,IAAA,CAACH,IAAI;MACH0B,KAAK,EAAE,CACLE,MAAM,CAACK,IAAI,EACX;QACEC,OAAO,EAAEd,aAAa;QACtB;QACAe,QAAQ,EAAER;MACZ,CAAC,CACD;MAAAK,QAAA,EAEDR,UAAU,CAAC;QACVY,OAAO,EAAE,IAAI;QACbnB,IAAI,EAAEU,QAAQ;QACdU,KAAK,EAAEf;MACT,CAAC;IAAC,CACE,CAAC,eACPnB,IAAA,CAACH,IAAI;MAAC0B,KAAK,EAAE,CAACE,MAAM,CAACK,IAAI,EAAE;QAAEC,OAAO,EAAEb;MAAgB,CAAC,CAAE;MAAAW,QAAA,EACtDR,UAAU,CAAC;QACVY,OAAO,EAAE,KAAK;QACdnB,IAAI,EAAEU,QAAQ;QACdU,KAAK,EAAEd;MACT,CAAC;IAAC,CACE,CAAC,eACPpB,IAAA,CAACF,KAAK;MACJqC,OAAO,EAAEpB,KAAK,IAAI,IAAK;MACvBD,IAAI,EAAEU,QAAQ,GAAG,IAAK;MACtBF,gBAAgB,EAAEA,gBAAiB;MACnCC,KAAK,EAAE,CAACE,MAAM,CAACV,KAAK,EAAEC,UAAU,CAAE;MAAAa,QAAA,EAEjCd;IAAK,CACD,CAAC;EAAA,CACJ,CAAC;AAEX;AAEA,MAAMU,MAAM,GAAG7B,UAAU,CAACwC,MAAM,CAAC;EAC/BN,IAAI,EAAE;IACJ;IACA;IACA;IACAO,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE;EACT,CAAC;EACDd,YAAY,EAAE;IACZc,KAAK,EAAEvC,cAAc;IACrBsC,MAAM,EAAEpC;EACV,CAAC;EACDsB,mBAAmB,EAAE;IACnBe,KAAK,EAAEtC,sBAAsB;IAC7BqC,MAAM,EAAEnC;EACV,CAAC;EACDoB,eAAe,EAAE;IACfgB,KAAK,EAAEjC,kBAAkB;IACzBgC,MAAM,EAAEhC;EACV,CAAC;EACDM,KAAK,EAAE;IACLsB,QAAQ,EAAE,UAAU;IACpBM,GAAG,EAAE,CAAC,CAAC;IACPC,GAAG,EAAE,CAAC;EACR;AACF,CAAC,CAAC", "ignoreList": []}