Object.defineProperty(exports,"__esModule",{value:true});exports.default=createIconSourceCache;var TYPE_VALUE='value';var TYPE_ERROR='error';function createIconSourceCache(){var cache=new Map();var setValue=function setValue(key,value){return cache.set(key,{type:TYPE_VALUE,data:value});};var setError=function setError(key,error){return cache.set(key,{type:TYPE_ERROR,data:error});};var has=function has(key){return cache.has(key);};var get=function get(key){if(!cache.has(key)){return undefined;}var _cache$get=cache.get(key),type=_cache$get.type,data=_cache$get.data;if(type===TYPE_ERROR){throw data;}return data;};return{setValue:setValue,setError:setError,has:has,get:get};}