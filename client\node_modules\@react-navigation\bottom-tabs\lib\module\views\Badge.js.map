{"version": 3, "names": ["useTheme", "color", "React", "Animated", "Platform", "StyleSheet", "jsx", "_jsx", "useNativeDriver", "OS", "Badge", "children", "style", "visible", "size", "rest", "opacity", "useState", "Value", "rendered", "setRendered", "colors", "fonts", "useEffect", "timing", "toValue", "duration", "start", "finished", "stopAnimation", "backgroundColor", "notification", "restStyle", "flatten", "textColor", "isLight", "borderRadius", "fontSize", "Math", "floor", "Text", "numberOfLines", "transform", "scale", "interpolate", "inputRange", "outputRange", "lineHeight", "height", "min<PERSON><PERSON><PERSON>", "regular", "styles", "container", "create", "alignSelf", "textAlign", "paddingHorizontal", "overflow"], "sourceRoot": "../../../src", "sources": ["views/Badge.tsx"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EACRC,QAAQ,EAERC,UAAU,QAGL,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAqBtB,MAAMC,eAAe,GAAGJ,QAAQ,CAACK,EAAE,KAAK,KAAK;AAE7C,OAAO,SAASC,KAAKA,CAAC;EACpBC,QAAQ;EACRC,KAAK;EACLC,OAAO,GAAG,IAAI;EACdC,IAAI,GAAG,EAAE;EACT,GAAGC;AACE,CAAC,EAAE;EACR,MAAM,CAACC,OAAO,CAAC,GAAGd,KAAK,CAACe,QAAQ,CAAC,MAAM,IAAId,QAAQ,CAACe,KAAK,CAACL,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAC3E,MAAM,CAACM,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,KAAK,CAACe,QAAQ,CAACJ,OAAO,CAAC;EAEvD,MAAM;IAAEQ,MAAM;IAAEC;EAAM,CAAC,GAAGtB,QAAQ,CAAC,CAAC;EAEpCE,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpB,IAAI,CAACJ,QAAQ,EAAE;MACb;IACF;IAEAhB,QAAQ,CAACqB,MAAM,CAACR,OAAO,EAAE;MACvBS,OAAO,EAAEZ,OAAO,GAAG,CAAC,GAAG,CAAC;MACxBa,QAAQ,EAAE,GAAG;MACblB;IACF,CAAC,CAAC,CAACmB,KAAK,CAAC,CAAC;MAAEC;IAAS,CAAC,KAAK;MACzB,IAAIA,QAAQ,IAAI,CAACf,OAAO,EAAE;QACxBO,WAAW,CAAC,KAAK,CAAC;MACpB;IACF,CAAC,CAAC;IAEF,OAAO,MAAMJ,OAAO,CAACa,aAAa,CAAC,CAAC;EACtC,CAAC,EAAE,CAACb,OAAO,EAAEG,QAAQ,EAAEN,OAAO,CAAC,CAAC;EAEhC,IAAI,CAACM,QAAQ,EAAE;IACb,IAAIN,OAAO,EAAE;MACXO,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAM;IAAEU,eAAe,GAAGT,MAAM,CAACU,YAAY;IAAE,GAAGC;EAAU,CAAC,GAC3D3B,UAAU,CAAC4B,OAAO,CAACrB,KAAK,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMsB,SAAS,GAAGjC,KAAK,CAAC6B,eAAe,CAAC,CAACK,OAAO,CAAC,CAAC,GAAG,OAAO,GAAG,OAAO;EAEtE,MAAMC,YAAY,GAAGtB,IAAI,GAAG,CAAC;EAC7B,MAAMuB,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAEzB,IAAI,GAAG,CAAC,GAAI,CAAC,CAAC;EAE3C,oBACEP,IAAA,CAACJ,QAAQ,CAACqC,IAAI;IACZC,aAAa,EAAE,CAAE;IACjB7B,KAAK,EAAE,CACL;MACE8B,SAAS,EAAE,CACT;QACEC,KAAK,EAAE3B,OAAO,CAAC4B,WAAW,CAAC;UACzBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC;QACtB,CAAC;MACH,CAAC,CACF;MACD7C,KAAK,EAAEiC,SAAS;MAChBa,UAAU,EAAEjC,IAAI,GAAG,CAAC;MACpBkC,MAAM,EAAElC,IAAI;MACZmC,QAAQ,EAAEnC,IAAI;MACdE,OAAO;MACPc,eAAe;MACfO,QAAQ;MACRD;IACF,CAAC,EACDd,KAAK,CAAC4B,OAAO,EACbC,MAAM,CAACC,SAAS,EAChBpB,SAAS,CACT;IAAA,GACEjB,IAAI;IAAAJ,QAAA,EAEPA;EAAQ,CACI,CAAC;AAEpB;AAEA,MAAMwC,MAAM,GAAG9C,UAAU,CAACgD,MAAM,CAAC;EAC/BD,SAAS,EAAE;IACTE,SAAS,EAAE,UAAU;IACrBC,SAAS,EAAE,QAAQ;IACnBC,iBAAiB,EAAE,CAAC;IACpBC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}